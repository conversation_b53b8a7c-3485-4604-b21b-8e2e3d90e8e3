package de.interzero.oneepr.customer.customer_commitment.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * Defines the structure for the response after a commitment submission.
 * Originally found in src/customer-commitment/dto/customer-commitment.dto.ts
 */
@Data
public class CommitmentSubmissionResponse {

    @JsonProperty("year")
    private Integer year;

    @JsonProperty("commitment")
    private List<AnsweredCommitment> commitment;

    @JsonProperty("setup")
    private CustomerServiceSetup setup;
}