package de.interzero.oneepr.customer.customer_commitment;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.customer.customer_commitment.dto.AnsweredCommitment;
import de.interzero.oneepr.customer.customer_commitment.dto.CustomerServiceSetup;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.shopping_cart.ShoppingCart;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;

/**
 * Response DTO for CustomerCommitment entity.
 * This DTO provides a clean API response structure without exposing internal entity details.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Slf4j
@Schema(description = "Customer commitment response")
public class CustomerCommitmentResponseDto {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Schema(
            description = "Unique identifier of the customer commitment",
            example = "1"
    )
    @JsonProperty("id")
    private Integer id;

    @Schema(
            description = "Customer email address",
            example = "<EMAIL>"
    )
    @JsonProperty("customer_email")
    private String customerEmail;

    @Schema(description = "Customer information")
    @JsonProperty("customer")
    private CustomerDto customer;

    @Schema(
            description = "Country code",
            example = "DE"
    )
    @JsonProperty("country_code")
    private String countryCode;

    @Schema(
            description = "Commitment year",
            example = "2024"
    )
    @JsonProperty("year")
    private Integer year;

    @Schema(description = "List of answered commitment criteria")
    @JsonProperty("commitment")
    private List<AnsweredCommitment> commitment;

    @Schema(description = "Service setup configuration")
    @JsonProperty("service_setup")
    private CustomerServiceSetup serviceSetup;

    @Schema(
            description = "Whether a license is required",
            example = "true"
    )
    @JsonProperty("is_license_required")
    private Boolean isLicenseRequired;

    @Schema(
            description = "Shopping cart ID",
            example = "cart-123"
    )
    @JsonProperty("shopping_cart_id")
    private String shoppingCartId;

    @Schema(description = "Shopping cart information")
    @JsonProperty("shopping_cart")
    private ShoppingCartDto shoppingCart;

    @Schema(description = "Creation timestamp")
    @JsonProperty("created_at")
    private Instant createdAt;

    @Schema(description = "Last update timestamp")
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Schema(description = "Deletion date (if soft deleted)")
    @JsonProperty("deleted_at")
    private LocalDate deletedAt;

    /**
     * Nested DTO for Customer information
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Customer information")
    public static class CustomerDto {

        @Schema(
                description = "Customer ID",
                example = "1"
        )
        @JsonProperty("id")
        private Integer id;

        @Schema(
                description = "Customer email",
                example = "<EMAIL>"
        )
        @JsonProperty("email")
        private String email;

        @Schema(
                description = "Customer first name",
                example = "John"
        )
        @JsonProperty("first_name")
        private String firstName;

        @Schema(
                description = "Customer last name",
                example = "Doe"
        )
        @JsonProperty("last_name")
        private String lastName;

        @Schema(
                description = "Customer company name",
                example = "ACME Corp"
        )
        @JsonProperty("company_name")
        private String companyName;
    }

    /**
     * Nested DTO for ShoppingCart information
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Shopping cart information")
    public static class ShoppingCartDto {

        @Schema(
                description = "Shopping cart ID",
                example = "cart-123"
        )
        @JsonProperty("id")
        private String id;

        @Schema(
                description = "Shopping cart status",
                example = "ACTIVE"
        )
        @JsonProperty("status")
        private String status;
    }

    /**
     * Factory method to create CustomerCommitmentResponseDto from CustomerCommitment entity
     *
     * @param entity The CustomerCommitment entity
     * @return CustomerCommitmentResponseDto
     */
    public static CustomerCommitmentResponseDto fromEntity(CustomerCommitment entity) {
        if (entity == null) {
            return null;
        }

        CustomerCommitmentResponseDtoBuilder builder = CustomerCommitmentResponseDto.builder()
                .id(entity.getId())
                .customerEmail(entity.getCustomerEmail())
                .countryCode(entity.getCountryCode())
                .year(entity.getYear())
                .isLicenseRequired(entity.getIsLicenseRequired())
                .shoppingCartId(entity.getShoppingCartId())
                .createdAt(entity.getCreatedAt())
                .updatedAt(entity.getUpdatedAt())
                .deletedAt(entity.getDeletedAt());

        // Convert commitment JSON to List<AnsweredCommitment>
        if (entity.getCommitment() != null) {
            try {
                List<AnsweredCommitment> commitmentList = objectMapper.convertValue(
                        entity.getCommitment(),
                        new TypeReference<List<AnsweredCommitment>>() {
                        });
                builder.commitment(commitmentList);
            } catch (Exception e) {
                log.warn("Failed to convert commitment JSON for entity {}: {}", entity.getId(), e.getMessage());
            }
        }

        // Convert serviceSetup JSON to CustomerServiceSetup
        if (entity.getServiceSetup() != null) {
            try {
                CustomerServiceSetup serviceSetup = objectMapper.convertValue(
                        entity.getServiceSetup(),
                        CustomerServiceSetup.class);
                builder.serviceSetup(serviceSetup);
            } catch (Exception e) {
                log.warn("Failed to convert serviceSetup JSON for entity {}: {}", entity.getId(), e.getMessage());
            }
        }

        // Convert customer if present
        if (entity.getCustomer() != null) {
            builder.customer(CustomerDto.builder()
                                     .id(entity.getCustomer().getId())
                                     .email(entity.getCustomer().getEmail())
                                     .firstName(entity.getCustomer().getFirstName())
                                     .lastName(entity.getCustomer().getLastName())
                                     .companyName(entity.getCustomer().getCompanyName())
                                     .build());
        }

        // Convert shopping cart if present
        if (entity.getShoppingCart() != null) {
            builder.shoppingCart(ShoppingCartDto.builder()
                                         .id(entity.getShoppingCart().getId())
                                         .status(entity.getShoppingCart().getStatus() != null ? entity.getShoppingCart()
                                                 .getStatus()
                                                 .toString() : null)
                                         .build());
        }

        return builder.build();
    }
}