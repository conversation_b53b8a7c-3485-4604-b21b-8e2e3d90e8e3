package de.interzero.oneepr.customer.customer_commitment;

import de.interzero.oneepr.common.AuthUtil;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.customer.customer_commitment.dto.CreateCustomerCommitmentDto;
import de.interzero.oneepr.customer.customer_commitment.dto.FindCustomerCommitmentDto;
import de.interzero.oneepr.customer.customer_commitment.dto.UpdateCustomerCommitmentDto;
import de.interzero.oneepr.customer.shared.auth.AuthenticatedUser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static de.interzero.oneepr.common.string.Role.ADMIN;
import static de.interzero.oneepr.common.string.Role.SUPER_ADMIN;

/**
 * REST Controller for managing customer commitments.
 *
 * @ts-legacy This class is a direct translation of the NestJS CustomerCommitmentController.
 * It uses a mix of public routes and secured routes, as defined in the original code.
 */
@RestController
@RequestMapping(Api.CUSTOMER_COMMITMENTS)
@RequiredArgsConstructor
@Tag(
        name = "Customer Commitments",
        description = "Endpoints for managing customer commitments."
)
public class CustomerCommitmentController {

    private final CustomerCommitmentService customerCommitmentService;

    /**
     * Creates a new customer commitment. This is a public endpoint.
     *
     * @param createDto The DTO containing the data for the new commitment.
     * @return The newly created customer commitment entity with a 201 status.
     * @ts-legacy Corresponds to the `create` method decorated with @PublicRoute.
     */
    @PostMapping
    @Operation(summary = "Create a new customer commitment")
    @ApiResponse(
            responseCode = "201",
            description = "Commitment created successfully"
    )
    @PreAuthorize("permitAll()")
    public ResponseEntity<CustomerCommitmentResponseDto> create(@RequestBody CreateCustomerCommitmentDto createDto) {
        CustomerCommitment createdCommitment = customerCommitmentService.create(createDto);
        CustomerCommitmentResponseDto responseDto = CustomerCommitmentResponseDto.fromEntity(createdCommitment);
        return new ResponseEntity<>(responseDto, HttpStatus.CREATED);
    }

    /**
     * Finds all customer commitments based on filter criteria. This is a public endpoint.
     *
     * @param query DTO containing optional filter parameters from the URL query string.
     * @return A list of matching customer commitments.
     * @ts-legacy Corresponds to the `findAll` method decorated with @PublicRoute.
     * Spring automatically binds query params to the DTO fields.
     */
    @GetMapping
    @Operation(summary = "Find all customer commitments")
    @ApiResponse(
            responseCode = "200",
            description = "A list of commitments"
    )
    public List<CustomerCommitmentResponseDto> findAll(FindCustomerCommitmentDto query) {
        List<CustomerCommitment> commitments = customerCommitmentService.findAll(query);
        return commitments.stream()
                .map(CustomerCommitmentResponseDto::fromEntity)
                .toList();
    }

    /**
     * Finds a single customer commitment by its ID. This is a public endpoint.
     *
     * @param id The ID of the commitment to retrieve.
     * @return The found customer commitment.
     * @ts-legacy Corresponds to the `findOne` method decorated with @PublicRoute.
     * It can optionally receive user context, which is handled by a utility method that
     * does not throw an error if the user is not authenticated.
     */
    @GetMapping("/{id}")
    @Operation(summary = "Find a customer commitment by ID")
    @ApiResponse(
            responseCode = "200",
            description = "The requested commitment"
    )
    public CustomerCommitmentResponseDto findOne(@PathVariable Integer id) {
        AuthenticatedUser user = AuthUtil.getRelevantUserDetails();
        CustomerCommitment commitment = customerCommitmentService.findOne(id, user);
        return CustomerCommitmentResponseDto.fromEntity(commitment);
    }

    /**
     * Updates an existing customer commitment. This is a public endpoint.
     *
     * @param id        The ID of the commitment to update.
     * @param updateDto The DTO with the data to update.
     * @return The updated customer commitment.
     * @ts-legacy Corresponds to the `update` method decorated with @PublicRoute.
     * This uses a PUT mapping, which is a direct translation from the source.
     */
    @PutMapping("/{id}")
    @Operation(summary = "Update a customer commitment by ID")
    @ApiResponse(
            responseCode = "200",
            description = "The updated commitment"
    )
    public CustomerCommitmentResponseDto update(@PathVariable Integer id,
                                               @RequestBody UpdateCustomerCommitmentDto updateDto) {
        AuthenticatedUser user = AuthUtil.getRelevantUserDetails();
        CustomerCommitment updatedCommitment = customerCommitmentService.update(id, updateDto, user);
        return CustomerCommitmentResponseDto.fromEntity(updatedCommitment);
    }

    /**
     * Deletes a customer commitment by its ID. This is a protected endpoint.
     *
     * @param id The ID of the commitment to delete.
     * @ts-legacy Corresponds to the `remove` method, which was not decorated with @PublicRoute
     * and therefore requires authentication.
     */
    @DeleteMapping("/{id}")
    @Secured({SUPER_ADMIN, ADMIN})
    @Operation(summary = "Delete a customer commitment by ID")
    @ApiResponse(
            responseCode = "204",
            description = "Commitment deleted successfully"
    )
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void remove(@PathVariable Integer id) {
        AuthenticatedUser user = AuthUtil.getRelevantUserDetails();
        customerCommitmentService.remove(id, user);
    }
}