package de.interzero.oneepr.admin.required_information.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.required_information.RequiredInformation;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Data Transfer Object for creating a new required information entry.
 */
@Getter
@Setter
public class CreateRequiredInformationDto extends BaseDto {

    @Schema(
            description = "ID of the country",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("country_id")
    private Integer countryId;

    @Schema(
            description = "Type of the required information",
            requiredMode = Schema.RequiredMode.REQUIRED,
            implementation = RequiredInformation.Type.class
    )
    @JsonProperty("type")
    private RequiredInformation.Type type;

    @Schema(
            description = "Name of the required information",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("name")
    private String name;

    @Schema(
            description = "Description of the required information",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("description")
    private String description;

    @Schema(description = "Question for the required information")
    @JsonProperty("question")
    private String question;

    @Schema(description = "ID of the associated file")
    @JsonProperty("file_id")
    private String fileId;

    @Schema(description = "IDs of the packaging service")
    @JsonProperty("packing_service_ids")
    private List<Integer> packingServiceIds;
}