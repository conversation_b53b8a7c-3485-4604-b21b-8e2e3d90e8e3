package de.interzero.oneepr.admin.price_list.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.price_list.PriceList;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class CreatePriceListDto extends BaseDto {

    @Schema(
            description = "Type of the price list",
            requiredMode = Schema.RequiredMode.REQUIRED,
            implementation = PriceList.Type.class
    )
    @JsonProperty("type")
    private PriceList.Type type;

    @Schema(
            description = "Name of the price list",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("name")
    private String name;

    @Schema(
            description = "Description of the price list",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("description")
    private String description;

    @Schema(
            description = "Condition type of the price list",
            requiredMode = Schema.RequiredMode.REQUIRED,
            implementation = PriceList.ConditionType.class
    )
    @JsonProperty("condition_type")
    private PriceList.ConditionType conditionType;

    @Schema(
            description = "Condition type value of the price list",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("condition_type_value")
    private String conditionTypeValue;

    @Schema(
            description = "Start date of the price list",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("start_date")
    private Instant startDate;

    @Schema(
            description = "End date of the price list",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("end_date")
    private Instant endDate;

    @Schema(description = "Basic price for EU_LICENSE or DIRECT_LICENSE")
    @JsonProperty("basic_price")
    private Integer basicPrice;

    @Schema(description = "Minimum price for EU_LICENSE or DIRECT_LICENSE")
    @JsonProperty("minimum_price")
    private Integer minimumPrice;

    @Schema(description = "Registration fee for EU_LICENSE or DIRECT_LICENSE")
    @JsonProperty("registration_fee")
    private Integer registrationFee;

    @Schema(description = "Variable handling fee for EU_LICENSE or DIRECT_LICENSE")
    @JsonProperty("handling_fee")
    private Integer handlingFee;

    @Schema(description = "Variable handling fee for EU_LICENSE or DIRECT_LICENSE")
    @JsonProperty("variable_handling_fee")
    private Double variableHandlingFee;

    @Schema(description = "Thresholds for DIRECT_LICENSE")
    @JsonProperty("thresholds")
    private List<ThresholdDto> thresholds;

    @Schema(description = "Price for ACTION_GUIDE or WORKSHOP")
    @JsonProperty("price")
    private Integer price;

    /**
     * Represents a single threshold object from the 'thresholds' array in the source NestJS DTO.
     * This class is necessary to model the elements of the array for type-safe processing in Java.
     */
    @Getter
    @Setter
    public static class ThresholdDto {
        @Schema(description = "Title of the threshold")
        @JsonProperty("title")
        private String title;

        @Schema(description = "Value of the threshold")
        @JsonProperty("value")
        private Integer value;

        @Schema(description = "Helper text for the threshold")
        @JsonProperty("helper_text")
        private String helperText;

        @Schema(description = "Fractions associated with the threshold")
        @JsonProperty("fractions")
        private Map<String, FractionDto> fractions;
    }

    /**
     * Represents the nested 'fraction' object within the 'thresholds' array from the source NestJS DTO.
     * This class is required for type-safe deserialization of the JSON request body by Jackson.
     */
    @Getter
    @Setter
    public static class FractionDto {
        @Schema(description = "Code of the fraction")
        @JsonProperty("code")
        private String code;

        @Schema(description = "Name of the fraction")
        @JsonProperty("name")
        private String name;

        @Schema(description = "Value of the fraction")
        @JsonProperty("value")
        private Integer value;
    }
}