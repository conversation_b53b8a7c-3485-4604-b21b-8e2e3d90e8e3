package de.interzero.oneepr.customer.customer_commitment;

import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.shopping_cart.ShoppingCart;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for CustomerCommitmentResponseDto
 */
class CustomerCommitmentResponseDtoTest {

    @Test
    void fromEntity_shouldConvertEntityToDto() {
        // Given
        CustomerCommitment entity = createTestEntity();

        // When
        CustomerCommitmentResponseDto dto = CustomerCommitmentResponseDto.fromEntity(entity);

        // Then
        assertNotNull(dto);
        assertEquals(entity.getId(), dto.getId());
        assertEquals(entity.getCustomerEmail(), dto.getCustomerEmail());
        assertEquals(entity.getCountryCode(), dto.getCountryCode());
        assertEquals(entity.getYear(), dto.getYear());
        assertEquals(entity.getIsLicenseRequired(), dto.getIsLicenseRequired());
        assertEquals(entity.getShoppingCartId(), dto.getShoppingCartId());
        assertEquals(entity.getCreatedAt(), dto.getCreatedAt());
        assertEquals(entity.getUpdatedAt(), dto.getUpdatedAt());
        assertEquals(entity.getDeletedAt(), dto.getDeletedAt());
    }

    @Test
    void fromEntity_shouldHandleNullEntity() {
        // When
        CustomerCommitmentResponseDto dto = CustomerCommitmentResponseDto.fromEntity(null);

        // Then
        assertNull(dto);
    }

    @Test
    void fromEntity_shouldConvertCustomerWhenPresent() {
        // Given
        CustomerCommitment entity = createTestEntity();
        Customer customer = createTestCustomer();
        entity.setCustomer(customer);

        // When
        CustomerCommitmentResponseDto dto = CustomerCommitmentResponseDto.fromEntity(entity);

        // Then
        assertNotNull(dto.getCustomer());
        assertEquals(customer.getId(), dto.getCustomer().getId());
        assertEquals(customer.getEmail(), dto.getCustomer().getEmail());
        assertEquals(customer.getFirstName(), dto.getCustomer().getFirstName());
        assertEquals(customer.getLastName(), dto.getCustomer().getLastName());
        assertEquals(customer.getCompanyName(), dto.getCustomer().getCompanyName());
    }

    @Test
    void fromEntity_shouldConvertShoppingCartWhenPresent() {
        // Given
        CustomerCommitment entity = createTestEntity();
        ShoppingCart shoppingCart = createTestShoppingCart();
        entity.setShoppingCart(shoppingCart);

        // When
        CustomerCommitmentResponseDto dto = CustomerCommitmentResponseDto.fromEntity(entity);

        // Then
        assertNotNull(dto.getShoppingCart());
        assertEquals(shoppingCart.getId(), dto.getShoppingCart().getId());
        assertEquals(shoppingCart.getStatus().toString(), dto.getShoppingCart().getStatus());
    }

    @Test
    void fromEntity_shouldHandleNullCustomerAndShoppingCart() {
        // Given
        CustomerCommitment entity = createTestEntity();
        entity.setCustomer(null);
        entity.setShoppingCart(null);

        // When
        CustomerCommitmentResponseDto dto = CustomerCommitmentResponseDto.fromEntity(entity);

        // Then
        assertNotNull(dto);
        assertNull(dto.getCustomer());
        assertNull(dto.getShoppingCart());
    }

    private CustomerCommitment createTestEntity() {
        CustomerCommitment entity = new CustomerCommitment();
        entity.setId(1);
        entity.setCustomerEmail("<EMAIL>");
        entity.setCountryCode("DE");
        entity.setYear(2024);
        entity.setIsLicenseRequired(true);
        entity.setShoppingCartId("cart-123");
        entity.setCreatedAt(Instant.now());
        entity.setUpdatedAt(Instant.now());
        entity.setDeletedAt(null);

        // Create test JSON data
        Map<String, Object> commitment = new HashMap<>();
        commitment.put("test", "data");
        entity.setCommitment(commitment);

        Map<String, Object> serviceSetup = new HashMap<>();
        serviceSetup.put("test", "setup");
        entity.setServiceSetup(serviceSetup);

        Map<String, Object> blame = new HashMap<>();
        blame.put("test", "blame");
        entity.setBlame(blame);

        return entity;
    }

    private Customer createTestCustomer() {
        Customer customer = new Customer();
        customer.setId(1);
        customer.setEmail("<EMAIL>");
        customer.setFirstName("John");
        customer.setLastName("Doe");
        customer.setCompanyName("ACME Corp");
        return customer;
    }

    private ShoppingCart createTestShoppingCart() {
        ShoppingCart shoppingCart = new ShoppingCart();
        shoppingCart.setId("cart-123");
        shoppingCart.setStatus(ShoppingCart.Status.OPEN);
        return shoppingCart;
    }
}
